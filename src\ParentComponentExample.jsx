import React, { useState } from 'react';
import DateRangeForm from './DateRangeForm';

export default function ParentComponentExample() {
  const [selectedDateRange, setSelectedDateRange] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [submissionHistory, setSubmissionHistory] = useState([]);

  // Handle when dates change (real-time updates)
  const handleDateChange = (dateData) => {
    console.log('Dates changed:', dateData);
    
    // You can perform real-time actions here
    if (dateData.isComplete) {
      console.log('Both dates selected!');
      // Example: Update some UI, validate against business rules, etc.
    }
  };

  // Handle form submission
  const handleDateRangeSubmit = async (dateRangeData) => {
    console.log('Date range submitted:', dateRangeData);
    setIsLoading(true);

    try {
      // Example: Send to API
      const response = await submitDateRangeToAPI(dateRangeData);
      
      // Update state with the submitted data
      setSelectedDateRange(dateRangeData);
      
      // Add to history
      setSubmissionHistory(prev => [...prev, {
        ...dateRangeData,
        submittedAt: new Date(),
        id: Date.now()
      }]);

      // Show success message
      alert('Date range submitted successfully!');
      
    } catch (error) {
      console.error('Error submitting date range:', error);
      alert('Error submitting date range. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Mock API call
  const submitDateRangeToAPI = async (dateRange) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Simulate API response
    return {
      success: true,
      data: dateRange,
      message: 'Date range saved successfully'
    };
  };

  // Clear selected date range
  const clearDateRange = () => {
    setSelectedDateRange(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">
          Date Range Form Integration Example
        </h1>

        {/* Date Range Form Component */}
        <div className="bg-white rounded-lg shadow-lg mb-8">
          <DateRangeForm
            onSubmit={handleDateRangeSubmit}
            onDateChange={handleDateChange}
            initialFromDate={null}
            initialToDate={null}
            maxDays={90} // Custom max days
            allowSameDate={false}
            title="Select Your Date Range"
            submitButtonText={isLoading ? "Submitting..." : "Submit Range"}
          />
        </div>

        {/* Current Selection Display */}
        {selectedDateRange && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-green-800 mb-4">
              Current Selected Range
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-green-600">From Date:</p>
                <p className="font-medium">{selectedDateRange.fromDate.toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-sm text-green-600">To Date:</p>
                <p className="font-medium">{selectedDateRange.toDate.toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-sm text-green-600">Duration:</p>
                <p className="font-medium">{selectedDateRange.daysDifference} days</p>
              </div>
              <div>
                <p className="text-sm text-green-600">Formatted Range:</p>
                <p className="font-medium">{selectedDateRange.formattedRange}</p>
              </div>
            </div>
            <button
              onClick={clearDateRange}
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Clear Selection
            </button>
          </div>
        )}

        {/* Submission History */}
        {submissionHistory.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-4">
              Submission History
            </h3>
            <div className="space-y-3">
              {submissionHistory.map((submission) => (
                <div key={submission.id} className="bg-white p-3 rounded border">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">
                      {submission.formattedRange}
                    </span>
                    <span className="text-sm text-gray-500">
                      {submission.submittedAt.toLocaleString()}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Duration: {submission.daysDifference} days
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Integration Code Example */}
        <div className="bg-gray-100 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold mb-4">Integration Code Example:</h3>
          <pre className="bg-gray-800 text-green-400 p-4 rounded text-sm overflow-x-auto">
{`<DateRangeForm
  onSubmit={handleDateRangeSubmit}
  onDateChange={handleDateChange}
  initialFromDate={null}
  initialToDate={null}
  maxDays={90}
  allowSameDate={false}
  title="Select Your Date Range"
  submitButtonText="Submit Range"
/>`}
          </pre>
        </div>
      </div>
    </div>
  );
}
