import './App.css'
import Calendar from './Calendar'
import WeekCalendar from './WeekCalendar'
import AdvancedCalendar from './AdvancedCalendar'
import DateRangeForm from './DateRangeForm'
import ParentComponentExample from './ParentComponentExample'
import { useState } from 'react'

function App() {
  const [showOriginalCalendar, setShowOriginalCalendar] = useState(false)
  const [showWeekCalendar, setShowWeekCalendar] = useState(false)
  const [showAdvancedCalendar, setShowAdvancedCalendar] = useState(false)
  const [showDateRangeForm, setShowDateRangeForm] = useState(false)
  const [showParentExample, setShowParentExample] = useState(false)
  const [calendarMode, setCalendarMode] = useState('all')

  const handleDateSelect = (date) => {
    console.log('Selected date:', date.toDateString());
  };

  const modes = [
    { value: 'all', label: 'All Dates Enabled' },
    { value: 'pastDisabled', label: 'Past Disabled' },
    { value: 'futureDisabled', label: 'Future Disabled' },
    { value: 'onlyToday', label: 'Only Today' }
  ];

  return (
    <>
      <div className="mt-8 flex justify-center gap-4 flex-wrap">
        <button 
          className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
          onClick={() => setShowOriginalCalendar(!showOriginalCalendar)}
        >
          {showOriginalCalendar ? 'Hide' : 'Show'} Original Calendar
        </button>
        <button 
          className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600"
          onClick={() => setShowWeekCalendar(!showWeekCalendar)}
        >
          {showWeekCalendar ? 'Hide' : 'Show'} Week Calendar
        </button>
        <button 
          className="bg-purple-500 text-white px-4 py-2 rounded-md hover:bg-purple-600"
          onClick={() => setShowAdvancedCalendar(!showAdvancedCalendar)}
        >
          {showAdvancedCalendar ? 'Hide' : 'Show'} Advanced Calendar
        </button>
        <button
          className="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600"
          onClick={() => setShowDateRangeForm(!showDateRangeForm)}
        >
          {showDateRangeForm ? 'Hide' : 'Show'} Date Range Form
        </button>
        <button
          className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600"
          onClick={() => setShowParentExample(!showParentExample)}
        >
          {showParentExample ? 'Hide' : 'Show'} Integration Example
        </button>
      </div>

      {/* Mode Selector - Only show when Advanced Calendar is visible */}
      {showAdvancedCalendar && (
        <div className="mt-4 flex justify-center">
          <div className="bg-gray-100 p-4 rounded-lg">
            <h3 className="text-center mb-3 text-lg font-semibold">Advanced Calendar Modes</h3>
            <div className="flex gap-2 flex-wrap justify-center">
              {modes.map((mode) => (
                <button
                  key={mode.value}
                  className={`px-3 py-1 rounded text-sm ${
                    calendarMode === mode.value
                      ? 'bg-blue-500 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-200'
                  }`}
                  onClick={() => setCalendarMode(mode.value)}
                >
                  {mode.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Original Calendar */}
      {showOriginalCalendar && (
        <div className="mt-8 flex justify-center">
          <div>
            <h3 className="text-center mb-4 text-lg font-semibold">Original Calendar</h3>
            <Calendar />
          </div>
        </div>
      )}

      {/* Week Calendar */}
      {showWeekCalendar && (
        <div className="mt-8 flex justify-center">
          <div>
            <h3 className="text-center mb-4 text-lg font-semibold">Week Selection Calendar</h3>
            <WeekCalendar />
          </div>
        </div>
      )}

      {/* Advanced Calendar */}
      {showAdvancedCalendar && (
        <div className="mt-8 flex justify-center">
          <div>
            <h3 className="text-center mb-4 text-lg font-semibold">Advanced Calendar</h3>
            <AdvancedCalendar 
              mode={calendarMode} 
              onDateSelect={handleDateSelect}
            />
          </div>
        </div>
      )}

      {/* Date Range Form */}
      {showDateRangeForm && (
        <div className="mt-8">
          <DateRangeForm />
        </div>
      )}
    </>
  )
}

export default App
