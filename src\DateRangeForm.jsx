import React, { useState } from "react";
import AdvancedCalendar from './AdvancedCalendar';

export default function DateRangeForm() {
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);
  const [showFromCalendar, setShowFromCalendar] = useState(false);
  const [showToCalendar, setShowToCalendar] = useState(false);

  const handleFromDateSelect = (date) => {
    setFromDate(date);
    setShowFromCalendar(false); // Hide calendar after selection
    // Reset "To" date if it's before the new "From" date
    if (toDate && toDate < date) {
      setToDate(null);
    }
  };

  const handleToDateSelect = (date) => {
    setToDate(date);
    setShowToCalendar(false); // Hide calendar after selection
  };

  const handleFromButtonClick = () => {
    setShowFromCalendar(true);
    setShowToCalendar(false); // Hide the other calendar
  };

  const handleToButtonClick = () => {
    setShowToCalendar(true);
    setShowFromCalendar(false); // Hide the other calendar
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h2 className="text-2xl font-bold text-center mb-8">Select Date Range</h2>

      {/* Date Selection Buttons */}
      <div className="flex justify-center gap-6 mb-8">
        <button
          onClick={handleFromButtonClick}
          className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
            fromDate
              ? 'bg-green-500 text-white hover:bg-green-600'
              : 'bg-blue-500 text-white hover:bg-blue-600'
          }`}
        >
          {fromDate ? `From: ${fromDate.toLocaleDateString()}` : 'Select From Date'}
        </button>

        <button
          onClick={handleToButtonClick}
          className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
            toDate
              ? 'bg-green-500 text-white hover:bg-green-600'
              : 'bg-purple-500 text-white hover:bg-purple-600'
          }`}
        >
          {toDate ? `To: ${toDate.toLocaleDateString()}` : 'Select To Date'}
        </button>
      </div>

      {/* Calendar Display Area */}
      <div className="flex justify-center">
        {showFromCalendar && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">Select From Date</h3>
            <AdvancedCalendar
              mode="pastDisabled"
              onDateSelect={handleFromDateSelect}
            />
            <div className="text-center">
              <button
                onClick={() => setShowFromCalendar(false)}
                className="text-sm text-gray-500 hover:text-gray-700 underline"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {showToCalendar && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">Select To Date</h3>
            <AdvancedCalendar
              mode="all"
              onDateSelect={handleToDateSelect}
            />
            <div className="text-center">
              <button
                onClick={() => setShowToCalendar(false)}
                className="text-sm text-gray-500 hover:text-gray-700 underline"
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Summary */}
      {fromDate && toDate && !showFromCalendar && !showToCalendar && (
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-center mb-2">Selected Range</h4>
          <p className="text-center">
            From: {fromDate.toLocaleDateString()} - To: {toDate.toLocaleDateString()}
          </p>
        </div>
      )}
    </div>
  );
}



 