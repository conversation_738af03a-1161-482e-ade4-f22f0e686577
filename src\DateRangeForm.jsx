import React, { useState } from "react";
import { MdKeyboardDoubleArrowRight } from "react-icons/md";
import { MdKeyboardDoubleArrowLeft } from "react-icons/md";
import { MdKeyboardArrowLeft } from "react-icons/md";
import { MdKeyboardArrowRight } from "react-icons/md";
import AdvancedCalendar from './AdvancedCalendar';

export default function DateRangeForm({
  onSubmit,
  onDateChange,
  initialFromDate = null,
  initialToDate = null,
  maxDays = 365,
  allowSameDate = false,
  title = "Select Date Range",
  submitButtonText = "Submit Date Range"
}) {
  const [fromDate, setFromDate] = useState(initialFromDate);
  const [toDate, setToDate] = useState(initialToDate);
  const [showFromCalendar, setShowFromCalendar] = useState(false);
  const [showToCalendar, setShowToCalendar] = useState(false);
  const [errors, setErrors] = useState({});
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Validation function
  const validateForm = () => {
    const newErrors = {};

    // Check if From Date is selected
    if (!fromDate) {
      newErrors.fromDate = 'From Date is required';
    }

    // Check if To Date is selected
    if (!toDate) {
      newErrors.toDate = 'To Date is required';
    }

    // Check if both dates are selected and validate range
    if (fromDate && toDate) {
      if (toDate < fromDate) {
        newErrors.dateRange = 'To Date must be after From Date';
      }

      // Check if date range is too long (configurable)
      const daysDifference = Math.ceil((toDate - fromDate) / (1000 * 60 * 60 * 24));
      if (daysDifference > maxDays) {
        newErrors.dateRange = `Date range cannot exceed ${maxDays} days`;
      }

      // Check if dates are the same (configurable validation)
      if (!allowSameDate && fromDate.getTime() === toDate.getTime()) {
        newErrors.dateRange = 'From Date and To Date cannot be the same';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Form submission handler
  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitted(true);

    if (validateForm()) {
      // Form is valid - call parent's onSubmit callback
      if (onSubmit) {
        onSubmit({
          fromDate,
          toDate,
          formattedRange: `${fromDate.toLocaleDateString()} - ${toDate.toLocaleDateString()}`,
          daysDifference: Math.ceil((toDate - fromDate) / (1000 * 60 * 60 * 24))
        });
      } else {
        // Fallback if no onSubmit provided
        console.log('Form submitted successfully!');
        alert(`Date Range Selected:\nFrom: ${fromDate.toLocaleDateString()}\nTo: ${toDate.toLocaleDateString()}`);
      }
    }
  };

  // Clear specific error when user interacts with field
  const clearError = (field) => {
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleFromDateSelect = (date) => {
    setFromDate(date);
    clearError('fromDate');
    clearError('dateRange');

    // Reset "To" date if it's before the new "From" date
    const newToDate = (toDate && toDate < date) ? null : toDate;
    if (toDate && toDate < date) {
      setToDate(null);
      clearError('toDate');
    }

    // Notify parent of date change
    if (onDateChange) {
      onDateChange({
        fromDate: date,
        toDate: newToDate,
        isComplete: date && newToDate
      });
    }
  };

  const handleToDateSelect = (date) => {
    setToDate(date);
    clearError('toDate');
    clearError('dateRange');

    // Notify parent of date change
    if (onDateChange) {
      onDateChange({
        fromDate,
        toDate: date,
        isComplete: fromDate && date
      });
    }
  };

  const handleFromButtonClick = () => {
    setShowFromCalendar(!showFromCalendar); // Toggle From calendar
    if (isSubmitted) clearError('fromDate');
  };

  const handleToButtonClick = () => {
    setShowToCalendar(!showToCalendar); // Toggle To calendar
    if (isSubmitted) clearError('toDate');
  };

  return (
    <form onSubmit={handleSubmit} className="max-w-4xl mx-auto p-6">
      <h2 className="text-2xl font-bold text-center mb-8">{title}</h2>

      {/* Date Selection Buttons */}
      <div className="flex justify-center gap-6 mb-4">
        <div className="flex flex-col items-center">
          <button
            type="button"
            onClick={handleFromButtonClick}
            className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
              fromDate
                ? 'bg-green-500 text-white hover:bg-green-600'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            } ${errors.fromDate ? 'ring-2 ring-red-500' : ''}`}
          >
            {fromDate ? `From: ${fromDate.toLocaleDateString()}` : 'Select From Date'}
          </button>
          {errors.fromDate && (
            <span className="text-red-500 text-sm mt-1">{errors.fromDate}</span>
          )}
        </div>

        <div className="flex flex-col items-center">
          <button
            type="button"
            onClick={handleToButtonClick}
            className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
              toDate
                ? 'bg-green-500 text-white hover:bg-green-600'
                : 'bg-purple-500 text-white hover:bg-purple-600'
            } ${errors.toDate ? 'ring-2 ring-red-500' : ''}`}
          >
            {toDate ? `To: ${toDate.toLocaleDateString()}` : 'Select To Date'}
          </button>
          {errors.toDate && (
            <span className="text-red-500 text-sm mt-1">{errors.toDate}</span>
          )}
        </div>
      </div>

      {/* Date Range Error */}
      {errors.dateRange && (
        <div className="text-center mb-4">
          <span className="text-red-500 text-sm">{errors.dateRange}</span>
        </div>
      )}

      {/* Calendar Display Area */}
      <div className="flex justify-center gap-8 flex-wrap">
        {showFromCalendar && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">Select From Date</h3>
            <AdvancedCalendar
              mode="pastDisabled"
              onDateSelect={handleFromDateSelect}
            />
          </div>
        )}

        {showToCalendar && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">Select To Date</h3>
            <CustomToCalendar
              fromDate={fromDate}
              onDateSelect={handleToDateSelect}
            />
          </div>
        )}
      </div>

      {/* Summary */}
      {fromDate && toDate && !showFromCalendar && !showToCalendar && (
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-center mb-2">Selected Range</h4>
          <p className="text-center">
            From: {fromDate.toLocaleDateString()} - To: {toDate.toLocaleDateString()}
          </p>
        </div>
      )}

      {/* Submit Button */}
      <div className="mt-8 flex justify-center">
        <button
          type="submit"
          className={`px-8 py-3 rounded-lg font-semibold transition-colors ${
            fromDate && toDate && Object.keys(errors).length === 0
              ? 'bg-green-600 text-white hover:bg-green-700'
              : 'bg-gray-400 text-gray-700 cursor-not-allowed'
          }`}
          disabled={!fromDate || !toDate}
        >
          {submitButtonText}
        </button>
      </div>

      {/* Form Status */}
      {isSubmitted && Object.keys(errors).length > 0 && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-center text-sm">
            Please fix the errors above before submitting.
          </p>
        </div>
      )}
    </form>
  );
}

// Custom "To" Calendar Component
function CustomToCalendar({ fromDate, onDateSelect }) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(null);

  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const days = getDaysInMonth(year, month);
  const startDay = new Date(year, month, 1).getDay();

  const prevMonth = () => setCurrentDate(new Date(year, month - 1, 1));
  const nextMonth = () => setCurrentDate(new Date(year, month + 1, 1));
  const prevYear = () => setCurrentDate(new Date(year - 1, month, 1));
  const nextYear = () => setCurrentDate(new Date(year + 1, month, 1));

  const handleDateClick = (clickedDate) => {
    // Check if date is disabled
    if (isDateDisabled(clickedDate)) {
      return;
    }

    setSelectedDate(clickedDate);
    if (onDateSelect) {
      onDateSelect(clickedDate);
    }
  };

  // Check if a date should be disabled
  const isDateDisabled = (date) => {
    if (!fromDate) return false; // If no "From" date, allow all dates

    const compareDate = new Date(date);
    const fromDateCompare = new Date(fromDate);

    // Set both dates to start of day for accurate comparison
    compareDate.setHours(0, 0, 0, 0);
    fromDateCompare.setHours(0, 0, 0, 0);

    // Disable dates before the "From" date
    return compareDate < fromDateCompare;
  };

  return (
    <div className="bg-white shadow-2xl rounded-2xl p-[20px] w-[380px] h-[277px] flex flex-col gap-2">
      {/* Header */}
      <div className="flex items-center justify-center gap-0 mb-2">
        <button className="text-md font-bold px-0 py-0 m-1" onClick={prevYear}>
          <MdKeyboardDoubleArrowLeft />
        </button>
        <button className="text-md font-bold px-0 py-0 m-1" onClick={prevMonth}>
          <MdKeyboardArrowLeft />
        </button>
        <span className="text-lg min-w-[120px] text-center text-gray-800">
          {currentDate.toLocaleString("default", { month: "long" })} {year}
        </span>
        <button className="text-md font-bold px-0 py-0 m-1" onClick={nextMonth}>
          <MdKeyboardArrowRight />
        </button>
        <button className="text-md font-bold px-0 py-0 m-1" onClick={nextYear}>
          <MdKeyboardDoubleArrowRight />
        </button>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 text-md gap-1 text-center flex-1">
        {/* Empty cells for alignment */}
        {Array(startDay).fill(null).map((_, i) => (
          <div key={"empty" + i}></div>
        ))}
        {/* Days of the month */}
        {days.map((date) => {
          const isDisabled = isDateDisabled(date);
          const isSelected = selectedDate && isSameDate(selectedDate, date);

          return (
            <div
              key={date.toISOString()}
              className={`rounded py-1 ${
                isDisabled
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'cursor-pointer hover:bg-gray-200'
              } ${
                isSelected ? 'text-white' : ''
              }`}
              style={{
                backgroundColor: isSelected ? '#323B83' : 'transparent'
              }}
              onClick={() => handleDateClick(date)}
            >
              {date.getDate()}
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Helper functions (same as in other calendar components)
function getDaysInMonth(year, month) {
  const date = new Date(year, month, 1);
  const days = [];
  while (date.getMonth() === month) {
    days.push(new Date(date));
    date.setDate(date.getDate() + 1);
  }
  return days;
}

function isSameDate(date1, date2) {
  return date1.getDate() === date2.getDate() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getFullYear() === date2.getFullYear();
}



 