import React, { useState } from "react";
import { MdKeyboardDoubleArrowRight } from "react-icons/md";
import { MdKeyboardDoubleArrowLeft } from "react-icons/md";
import { MdKeyboardArrowLeft } from "react-icons/md";
import { MdKeyboardArrowRight } from "react-icons/md";
import Calendar from './Calendar';
import AdvancedCalendar from './AdvancedCalendar';

export default function DateRangeForm() {
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);

  const handleFromDateSelect = (date) => {
    setFromDate(date);
    // Reset "To" date if it's before the new "From" date
    if (toDate && toDate < date) {
      setToDate(null);
    }
  };

  const handleToDateSelect = (date) => {
    setToDate(date);
  };



  return (
    <div className="max-w-4xl mx-auto p-6">
      <h2 className="text-2xl font-bold text-center mb-8">Select Date Range</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* From Calendar */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-center">From Date</h3>
          <AdvancedCalendar 
            mode="pastDisabled" 
            onDateSelect={handleFromDateSelect}
          />
          {fromDate && (
            <div className="text-center text-sm text-gray-600">
              Selected: {fromDate.toLocaleDateString()}
            </div>
          )}
        </div>

        {/* To Calendar */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-center">To Date</h3>
          <CustomToCalendar 
            fromDate={fromDate}
            onDateSelect={handleToDateSelect}
          />
          {toDate && (
            <div className="text-center text-sm text-gray-600">
              Selected: {toDate.toLocaleDateString()}
            </div>
          )}
        </div>
      </div>

      {/* Summary */}
      {fromDate && toDate && (
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-center mb-2">Selected Range</h4>
          <p className="text-center">
            From: {fromDate.toLocaleDateString()} - To: {toDate.toLocaleDateString()}
          </p>
        </div>
      )}
    </div>
  );
}

// Custom "To" Calendar Component
function CustomToCalendar({ fromDate, onDateSelect }) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(null);

  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const days = getDaysInMonth(year, month);
  const startDay = new Date(year, month, 1).getDay();

  const prevMonth = () => setCurrentDate(new Date(year, month - 1, 1));
  const nextMonth = () => setCurrentDate(new Date(year, month + 1, 1));
  const prevYear = () => setCurrentDate(new Date(year - 1, month, 1));
  const nextYear = () => setCurrentDate(new Date(year + 1, month, 1));

  const handleDateClick = (clickedDate) => {
    // Check if date is disabled
    if (isDateDisabled(clickedDate)) {
      return;
    }
    
    setSelectedDate(clickedDate);
    if (onDateSelect) {
      onDateSelect(clickedDate);
    }
  };

  // Check if a date should be disabled
  const isDateDisabled = (date) => {
    if (!fromDate) return false; // If no "From" date, allow all dates
    
    const compareDate = new Date(date);
    const fromDateCompare = new Date(fromDate);
    
    // Set both dates to start of day for accurate comparison
    compareDate.setHours(0, 0, 0, 0);
    fromDateCompare.setHours(0, 0, 0, 0);
    
    // Disable dates before the "From" date
    return compareDate < fromDateCompare;
  };

  return (
    <div className="bg-white shadow-2xl rounded-2xl p-[20px] w-[380px] h-[277px] flex flex-col gap-2">
      {/* Header */}
      <div className="flex items-center justify-center gap-0 mb-2">
        <button className="text-md font-bold px-0 py-0 m-1" onClick={prevYear}>
          <MdKeyboardDoubleArrowLeft />
        </button>
        <button className="text-md font-bold px-0 py-0 m-1" onClick={prevMonth}>
          <MdKeyboardArrowLeft />
        </button>
        <span className="text-lg min-w-[120px] text-center text-gray-800">
          {currentDate.toLocaleString("default", { month: "long" })} {year}
        </span>
        <button className="text-md font-bold px-0 py-0 m-1" onClick={nextMonth}>
          <MdKeyboardArrowRight />
        </button>
        <button className="text-md font-bold px-0 py-0 m-1" onClick={nextYear}>
          <MdKeyboardDoubleArrowRight />
        </button>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 text-md gap-1 text-center flex-1">
        {/* Empty cells for alignment */}
        {Array(startDay).fill(null).map((_, i) => (
          <div key={"empty" + i}></div>
        ))}
        {/* Days of the month */}
        {days.map((date) => {
          const isDisabled = isDateDisabled(date);
          const isSelected = selectedDate && isSameDate(selectedDate, date);
          
          return (
            <div
              key={date.toISOString()}
              className={`rounded py-1 ${
                isDisabled 
                  ? 'text-gray-400 cursor-not-allowed' 
                  : 'cursor-pointer hover:bg-gray-200'
              } ${
                isSelected ? 'text-white' : ''
              }`}
              style={{
                backgroundColor: isSelected ? '#323B83' : 'transparent'
              }}
              onClick={() => handleDateClick(date)}
            >
              {date.getDate()}
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Helper functions (same as in other calendar components)
function getDaysInMonth(year, month) {
  const date = new Date(year, month, 1);
  const days = [];
  while (date.getMonth() === month) {
    days.push(new Date(date));
    date.setDate(date.getDate() + 1);
  }
  return days;
}

function isSameDate(date1, date2) {
  return date1.getDate() === date2.getDate() && 
         date1.getMonth() === date2.getMonth() && 
         date1.getFullYear() === date2.getFullYear();
}

 